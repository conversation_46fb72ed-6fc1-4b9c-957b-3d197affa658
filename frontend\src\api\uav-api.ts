import apiClient, { createQueryString, handleAPIResponse } from '@/lib/api-client';
import {
  UAV,
  CreateUAVRequest,
  UpdateUAVRequest,
  UAVFilter,
  PaginationParams,
  SystemStatistics,
  HibernatePodStatus,
  Region,
  APIResponse,
} from '@/types/uav';

export class UAVApi {
  private basePath = '/api/uav';

  // Get all UAVs with optional filtering and pagination
  async getUAVs(filter?: UAVFilter, pagination?: PaginationParams): Promise<UAV[]> {
    const params = {
      ...filter,
      ...pagination,
    };
    
    const queryString = createQueryString(params);
    const url = queryString ? `${this.basePath}/all?${queryString}` : `${this.basePath}/all`;
    
    return apiClient.get<UAV[]>(url);
  }

  // Get UAV by ID
  async getUAVById(id: number): Promise<UAV> {
    return apiClient.get<UAV>(`${this.basePath}/${id}`);
  }

  // Create new UAV
  async createUAV(uav: CreateUAVRequest): Promise<APIResponse<UAV>> {
    return apiClient.post<APIResponse<UAV>>(`${this.basePath}`, uav);
  }

  // Update existing UAV
  async updateUAV(id: number, uav: Partial<UpdateUAVRequest>): Promise<APIResponse<UAV>> {
    return apiClient.put<APIResponse<UAV>>(`${this.basePath}/${id}`, uav);
  }

  // Delete UAV
  async deleteUAV(id: number): Promise<APIResponse<void>> {
    return apiClient.delete<APIResponse<void>>(`${this.basePath}/${id}`);
  }

  // Update UAV status
  async updateUAVStatus(id: number): Promise<APIResponse<any>> {
    return apiClient.put<APIResponse<any>>(`${this.basePath}/${id}/status`);
  }

  // Validate RFID tag uniqueness
  async validateRfidTag(rfidTag: string, excludeId?: number): Promise<{ isUnique: boolean }> {
    const params = excludeId ? { excludeId } : {};
    const queryString = createQueryString(params);
    const url = queryString 
      ? `${this.basePath}/validate-rfid/${encodeURIComponent(rfidTag)}?${queryString}`
      : `${this.basePath}/validate-rfid/${encodeURIComponent(rfidTag)}`;
    
    return apiClient.get<{ isUnique: boolean }>(url);
  }

  // Add region to UAV
  async addRegionToUAV(uavId: number, regionId: number): Promise<APIResponse<UAV>> {
    return apiClient.post<APIResponse<UAV>>(`${this.basePath}/${uavId}/regions/${regionId}`);
  }

  // Remove region from UAV
  async removeRegionFromUAV(uavId: number, regionId: number): Promise<APIResponse<void>> {
    return apiClient.delete<APIResponse<void>>(`${this.basePath}/${uavId}/regions/${regionId}`);
  }

  // Get available regions for UAV (not already assigned)
  async getAvailableRegionsForUAV(uavId: number): Promise<Region[]> {
    return apiClient.get<Region[]>(`${this.basePath}/${uavId}/available-regions`);
  }

  // Get system statistics
  async getSystemStatistics(): Promise<SystemStatistics> {
    return apiClient.get<SystemStatistics>(`${this.basePath}/statistics`);
  }

  // Search UAVs
  async searchUAVs(query: string): Promise<UAV[]> {
    const params = { search: query };
    const queryString = createQueryString(params);
    return apiClient.get<UAV[]>(`${this.basePath}/search?${queryString}`);
  }

  // Get UAVs by status
  async getUAVsByStatus(status: string): Promise<UAV[]> {
    const params = { status };
    const queryString = createQueryString(params);
    return apiClient.get<UAV[]>(`${this.basePath}/all?${queryString}`);
  }

  // Get UAVs by region
  async getUAVsByRegion(regionId: number): Promise<UAV[]> {
    const params = { regionId };
    const queryString = createQueryString(params);
    return apiClient.get<UAV[]>(`${this.basePath}/all?${queryString}`);
  }

  // Bulk operations
  async bulkUpdateStatus(uavIds: number[], status: string): Promise<APIResponse<void>> {
    return apiClient.post<APIResponse<void>>(`${this.basePath}/bulk/status`, {
      uavIds,
      status,
    });
  }

  async bulkDelete(uavIds: number[]): Promise<APIResponse<void>> {
    return apiClient.post<APIResponse<void>>(`${this.basePath}/bulk/delete`, {
      uavIds,
    });
  }

  // Export UAVs data
  async exportUAVs(format: 'csv' | 'excel' = 'csv'): Promise<Blob> {
    const response = await apiClient.get(`${this.basePath}/export?format=${format}`, {
      responseType: 'blob',
    });
    return response;
  }

  // Import UAVs data
  async importUAVs(file: File, onProgress?: (progress: number) => void): Promise<APIResponse<any>> {
    return apiClient.uploadFile<APIResponse<any>>(`${this.basePath}/import`, file, onProgress);
  }
}

// Hibernate Pod API
export class HibernatePodApi {
  private basePath = '/api/hibernate-pod';

  // Get hibernate pod status
  async getStatus(): Promise<HibernatePodStatus> {
    return apiClient.get<HibernatePodStatus>(`${this.basePath}/status`);
  }

  // Add UAV to hibernate pod
  async addUAV(uavId: number): Promise<APIResponse<any>> {
    return apiClient.post<APIResponse<any>>(`${this.basePath}/add`, { uavId });
  }

  // Remove UAV from hibernate pod
  async removeUAV(uavId: number): Promise<APIResponse<any>> {
    return apiClient.post<APIResponse<any>>(`${this.basePath}/remove`, { uavId });
  }

  // Get UAVs in hibernate pod
  async getUAVsInPod(): Promise<UAV[]> {
    return apiClient.get<UAV[]>(`${this.basePath}/uavs`);
  }

  // Update hibernate pod capacity
  async updateCapacity(maxCapacity: number): Promise<APIResponse<any>> {
    return apiClient.put<APIResponse<any>>(`${this.basePath}/capacity`, { maxCapacity });
  }
}

// Region API
export class RegionApi {
  private basePath = '/api/regions';

  // Get all regions
  async getRegions(): Promise<Region[]> {
    return apiClient.get<Region[]>(this.basePath);
  }

  // Get region by ID
  async getRegionById(id: number): Promise<Region> {
    return apiClient.get<Region>(`${this.basePath}/${id}`);
  }

  // Create new region
  async createRegion(region: { regionName: string }): Promise<APIResponse<Region>> {
    return apiClient.post<APIResponse<Region>>(this.basePath, region);
  }

  // Update region
  async updateRegion(id: number, region: { regionName: string }): Promise<APIResponse<Region>> {
    return apiClient.put<APIResponse<Region>>(`${this.basePath}/${id}`, region);
  }

  // Delete region
  async deleteRegion(id: number): Promise<APIResponse<void>> {
    return apiClient.delete<APIResponse<void>>(`${this.basePath}/${id}`);
  }

  // Get UAVs in region
  async getUAVsInRegion(regionId: number): Promise<UAV[]> {
    return apiClient.get<UAV[]>(`${this.basePath}/${regionId}/uavs`);
  }
}

// Create singleton instances
export const uavApi = new UAVApi();
export const hibernatePodApi = new HibernatePodApi();
export const regionApi = new RegionApi();

// Export default as UAV API for convenience
export default uavApi;
