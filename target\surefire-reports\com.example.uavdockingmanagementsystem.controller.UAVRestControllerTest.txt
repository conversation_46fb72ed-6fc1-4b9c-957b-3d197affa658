-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest
-------------------------------------------------------------------------------
Tests run: 22, Failures: 22, Errors: 0, Skipped: 0, Time elapsed: 0.879 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest
com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVsByStatus -- Time elapsed: 0.013 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVsByStatus(UAVRestControllerTest.java:253)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testUpdateUAV -- Time elapsed: 0.088 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testUpdateUAV(UAVRestControllerTest.java:360)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVRegionsNotFound -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVRegionsNotFound(UAVRestControllerTest.java:201)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testDeleteUAVNotFound -- Time elapsed: 0.004 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testDeleteUAVNotFound(UAVRestControllerTest.java:415)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVsByStatusInvalid -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVsByStatusInvalid(UAVRestControllerTest.java:265)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testToggleUAVStatus -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testToggleUAVStatus(UAVRestControllerTest.java:107)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetAllUAVs -- Time elapsed: 0.009 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetAllUAVs(UAVRestControllerTest.java:78)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testCreateUAVInvalidData -- Time elapsed: 0.005 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testCreateUAVInvalidData(UAVRestControllerTest.java:307)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetAvailableRegions -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetAvailableRegions(UAVRestControllerTest.java:212)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testDeleteUAV -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testDeleteUAV(UAVRestControllerTest.java:401)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testToggleUAVStatusNotFound -- Time elapsed: 0.010 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testToggleUAVStatusNotFound(UAVRestControllerTest.java:123)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testRemoveRegionFromUAV -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testRemoveRegionFromUAV(UAVRestControllerTest.java:160)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVRegions -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetUAVRegions(UAVRestControllerTest.java:187)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testValidateRfidTag -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testValidateRfidTag(UAVRestControllerTest.java:226)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetAllUAVsEmpty -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testGetAllUAVsEmpty(UAVRestControllerTest.java:93)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testCreateUAV -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<201> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testCreateUAV(UAVRestControllerTest.java:285)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testAddRegionToUAVFailure -- Time elapsed: 0.005 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testAddRegionToUAVFailure(UAVRestControllerTest.java:147)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testCreateUAVDuplicateRfid -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<409> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testCreateUAVDuplicateRfid(UAVRestControllerTest.java:332)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testValidateRfidTagNotUnique -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testValidateRfidTagNotUnique(UAVRestControllerTest.java:239)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testRemoveRegionFromUAVFailure -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testRemoveRegionFromUAVFailure(UAVRestControllerTest.java:173)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testUpdateUAVNotFound -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testUpdateUAVNotFound(UAVRestControllerTest.java:386)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testAddRegionToUAV -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.UAVRestControllerTest.testAddRegionToUAV(UAVRestControllerTest.java:134)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

