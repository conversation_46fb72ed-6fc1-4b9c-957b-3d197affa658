-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.service.RegionServiceTest
-------------------------------------------------------------------------------
Tests run: 27, Failures: 0, Errors: 27, Skipped: 0, Time elapsed: 0.044 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.service.RegionServiceTest
com.example.uavdockingmanagementsystem.service.RegionServiceTest.testCreateRegionWithValidation -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testCreateRegion -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionStatistics -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testValidateRegionName -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testBulkCreateRegionsWithDuplicates -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetUAVsByRegion -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testSearchRegionsNullQuery -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testBulkCreateRegions -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionsWithUAVCount -- Time elapsed: 0.002 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionByIdNotFound -- Time elapsed: 0.002 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionByName -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetUAVsByRegionWithFallback -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetUAVsByRegionEmpty -- Time elapsed: 0.002 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionById -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetAllRegions -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testSearchRegions -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testUpdateRegion -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testDeleteRegion -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testCreateRegionWithValidationInvalidName -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testUpdateRegionNotFound -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionsWithUAVCountEmpty -- Time elapsed: 0.002 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testSearchRegionsEmptyQuery -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionByNameNotFound -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testGetRegionByNameCaseInsensitive -- Time elapsed: 0 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testCreateRegionWithValidationDuplicate -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testSearchRegionsEmpty -- Time elapsed: 0.001 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.RegionServiceTest.testIsRegionNameUnique -- Time elapsed: 0.002 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problems: 
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method updateRegion(int, String) in the type RegionService is not applicable for the arguments (int, Region)
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method getRegionByName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method getRegionByNameIgnoreCase(String) is undefined for the type RegionService
	The method findByRegionNameIgnoreCase(String) is undefined for the type RegionRepository
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(null) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method validateRegionName(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method isRegionNameUnique(String, null) is undefined for the type RegionService
	The method isRegionNameUnique(String, int) is undefined for the type RegionService
	The method getRegionStatistics() is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(String) is undefined for the type RegionService
	The method findByRegionNameContainingIgnoreCase(String) is undefined for the type RegionRepository
	The method searchRegions(null) is undefined for the type RegionService
	The method searchRegions(String) is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method getRegionsWithUAVCount() is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method createRegionWithValidation(null) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method createRegionWithValidation(String) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method findByRegionName(String) is undefined for the type RegionRepository
	The method bulkCreateRegions(List<String>) is undefined for the type RegionService
	The method findByRegionName(String) is undefined for the type RegionRepository

	at com.example.uavdockingmanagementsystem.service.RegionServiceTest.<init>(RegionServiceTest.java:106)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

