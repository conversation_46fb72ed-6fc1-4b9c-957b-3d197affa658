-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest
-------------------------------------------------------------------------------
Tests run: 20, Failures: 20, Errors: 0, Skipped: 0, Time elapsed: 1.212 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest
com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testCreateStation -- Time elapsed: 0.232 s <<< FAILURE!
java.lang.AssertionError: Status expected:<201> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testCreateStation(DockingStationControllerTest.java:126)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testFindOptimalStation -- Time elapsed: 0.013 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testFindOptimalStation(DockingStationControllerTest.java:261)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetAvailableStations -- Time elapsed: 0.010 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetAvailableStations(DockingStationControllerTest.java:187)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUpdateStation -- Time elapsed: 0.009 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUpdateStation(DockingStationControllerTest.java:145)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationsByType -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationsByType(DockingStationControllerTest.java:290)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationsInArea -- Time elapsed: 0.004 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationsInArea(DockingStationControllerTest.java:333)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationsByTypeInvalid -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationsByTypeInvalid(DockingStationControllerTest.java:301)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationById -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationById(DockingStationControllerTest.java:98)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetAllStations -- Time elapsed: 0.009 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetAllStations(DockingStationControllerTest.java:69)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUpdateStationNotFound -- Time elapsed: 0.005 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUpdateStationNotFound(DockingStationControllerTest.java:163)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testFindOptimalStationNotFound -- Time elapsed: 0.010 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testFindOptimalStationNotFound(DockingStationControllerTest.java:278)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testDeleteStation -- Time elapsed: 0.011 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testDeleteStation(DockingStationControllerTest.java:174)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUndockUAV -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUndockUAV(DockingStationControllerTest.java:244)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetNearestStations -- Time elapsed: 0.005 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetNearestStations(DockingStationControllerTest.java:314)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationByIdNotFound -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<404> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationByIdNotFound(DockingStationControllerTest.java:112)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetAllStationsEmpty -- Time elapsed: 0.005 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetAllStationsEmpty(DockingStationControllerTest.java:85)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testDockUAV -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testDockUAV(DockingStationControllerTest.java:207)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testDockUAVFailure -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testDockUAVFailure(DockingStationControllerTest.java:226)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationStatistics -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testGetStationStatistics(DockingStationControllerTest.java:351)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUpdateStationStatus -- Time elapsed: 0.012 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.DockingStationControllerTest.testUpdateStationStatus(DockingStationControllerTest.java:375)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

