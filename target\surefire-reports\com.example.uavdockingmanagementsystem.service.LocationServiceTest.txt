-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.service.LocationServiceTest
-------------------------------------------------------------------------------
Tests run: 21, Failures: 5, Errors: 11, Skipped: 0, Time elapsed: 0.309 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.service.LocationServiceTest
com.example.uavdockingmanagementsystem.service.LocationServiceTest.testUpdateUAVLocation -- Time elapsed: 0.009 s <<< FAILURE!
Wanted but not invoked:
geofenceService.checkGeofenceViolations(
    40.713d,
    -74.0058d,
    55.0d
);
-> at com.example.uavdockingmanagementsystem.service.GeofenceService.checkGeofenceViolations(GeofenceService.java:182)
Actually, there were zero interactions with this mock.

	at com.example.uavdockingmanagementsystem.service.GeofenceService.checkGeofenceViolations(GeofenceService.java:182)
	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testUpdateUAVLocation(LocationServiceTest.java:86)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetNearbyUAVsEmpty -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	Type mismatch: cannot convert from List<Map<String,Object>> to List<UAV>

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetNearbyUAVsEmpty(LocationServiceTest.java:293)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetUAVsInArea -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	Type mismatch: cannot convert from List<Map<String,Object>> to List<UAV>

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetUAVsInArea(LocationServiceTest.java:147)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testCheckGeofenceViolationsNoViolations -- Time elapsed: 0.006 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method convertAndSend(String, Object) is ambiguous for the type SimpMessagingTemplate

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testCheckGeofenceViolationsNoViolations(LocationServiceTest.java:266)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testIsWithinRadius -- Time elapsed: 0.003 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <false> but was: <true>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertFalse.failNotFalse(AssertFalse.java:63)
	at org.junit.jupiter.api.AssertFalse.assertFalse(AssertFalse.java:36)
	at org.junit.jupiter.api.AssertFalse.assertFalse(AssertFalse.java:31)
	at org.junit.jupiter.api.Assertions.assertFalse(Assertions.java:231)
	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testIsWithinRadius(LocationServiceTest.java:187)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testCheckGeofenceViolations -- Time elapsed: 0.003 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method convertAndSend(String, Object) is ambiguous for the type SimpMessagingTemplate

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testCheckGeofenceViolations(LocationServiceTest.java:256)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testUpdateUAVLocationNullValues -- Time elapsed: 0.006 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testUpdateUAVLocationNullValues(LocationServiceTest.java:303)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:197)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetLocationStatistics -- Time elapsed: 0.005 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <50> but was: <0>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetLocationStatistics(LocationServiceTest.java:203)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetRealTimeTrackingData -- Time elapsed: 0.005 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method getRealTimeTrackingData() is undefined for the type LocationService

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetRealTimeTrackingData(LocationServiceTest.java:226)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testBroadcastLocationUpdate -- Time elapsed: 0.003 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method broadcastLocationUpdate(UAV) from the type LocationService is not visible

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testBroadcastLocationUpdate(LocationServiceTest.java:243)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetCurrentLocations -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	Type mismatch: cannot convert from List<Map<String,Object>> to List<UAV>

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetCurrentLocations(LocationServiceTest.java:135)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testUpdateUAVLocationWithGeofenceViolation -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	The method convertAndSend(String, Object) is ambiguous for the type SimpMessagingTemplate

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testUpdateUAVLocationWithGeofenceViolation(LocationServiceTest.java:101)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetLocationHistoryInTimeRange -- Time elapsed: 0.004 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <1> but was: <0>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:150)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:145)
	at org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:531)
	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetLocationHistoryInTimeRange(LocationServiceTest.java:125)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetUAVsInAreaEmpty -- Time elapsed: 0.004 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	Type mismatch: cannot convert from List<Map<String,Object>> to List<UAV>

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetUAVsInAreaEmpty(LocationServiceTest.java:283)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testLocationHistoryCreation -- Time elapsed: 0.006 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: not <null>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:152)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertNotNull.failNull(AssertNotNull.java:49)
	at org.junit.jupiter.api.AssertNotNull.assertNotNull(AssertNotNull.java:35)
	at org.junit.jupiter.api.AssertNotNull.assertNotNull(AssertNotNull.java:30)
	at org.junit.jupiter.api.Assertions.assertNotNull(Assertions.java:304)
	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.lambda$0(LocationServiceTest.java:325)
	at org.mockito.internal.stubbing.StubbedInvocationMatcher.answer(StubbedInvocationMatcher.java:42)
	at org.mockito.internal.handler.MockHandlerImpl.handle(MockHandlerImpl.java:103)
	at org.mockito.internal.handler.NullResultGuardian.handle(NullResultGuardian.java:29)
	at org.mockito.internal.handler.InvocationNotifierHandler.handle(InvocationNotifierHandler.java:34)
	at org.mockito.internal.creation.bytebuddy.MockMethodInterceptor.doIntercept(MockMethodInterceptor.java:82)
	at org.mockito.internal.creation.bytebuddy.MockMethodInterceptor.doIntercept(MockMethodInterceptor.java:56)
	at org.mockito.internal.creation.bytebuddy.MockMethodInterceptor$DispatcherDefaultingToRealMethod.interceptAbstract(MockMethodInterceptor.java:161)
	at com.example.uavdockingmanagementsystem.repository.LocationHistoryRepository$MockitoMock$pYzLE7j2.save(Unknown Source)
	at com.example.uavdockingmanagementsystem.repository.LocationHistoryRepository$MockitoMock$pYzLE7j2.save(Unknown Source)
	at com.example.uavdockingmanagementsystem.service.LocationService.updateUAVLocation(LocationService.java:237)
	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testLocationHistoryCreation(LocationServiceTest.java:330)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetNearbyUAVs -- Time elapsed: 0.003 s <<< ERROR!
java.lang.Error: 
Unresolved compilation problem: 
	Type mismatch: cannot convert from List<Map<String,Object>> to List<UAV>

	at com.example.uavdockingmanagementsystem.service.LocationServiceTest.testGetNearbyUAVs(LocationServiceTest.java:159)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

