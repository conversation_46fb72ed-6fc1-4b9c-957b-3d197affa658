# Compiled class files
*.class

# Log files
*.log
logs/
log/

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Spring Boot
*.pid
*.pid.lock
*.log.*
application-*.properties
application-*.yml
!application.properties
!application.yml
!application-local.properties
!application-advanced.yml

# Database
*.db
*.sqlite
*.sqlite3
h2/
hsqldb/

# Docker
.dockerignore

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Security and sensitive files
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore
secrets/
.env
.env.local
.env.production
.env.staging

# Test coverage
coverage/
.nyc_output/
*.lcov

# Node.js (if any frontend components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Generated documentation
docs/generated/
javadoc/

# Backup files
*.bak
*.backup
*.orig

# Cache directories
.cache/
.m2/repository/

# Application specific
uploads/
downloads/
temp/
cache/

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# STS (Spring Tool Suite)
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android Studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

# JProfiler
*.jprofiler

# YourKit
*.yjp

# Maven wrapper
!.mvn/wrapper/maven-wrapper.properties
maven.zip

# Application logs
application.log
audit.log
security.log

# Local development files
local/
dev/
development/

# Deployment artifacts
deploy/
deployment/
dist/

# Configuration overrides
config/local/
config/dev/
config/prod/

# SSL certificates
*.crt
*.cert
*.ca-bundle

# Database dumps
*.sql.gz
*.dump

# Monitoring and metrics
metrics/
monitoring/

# Kubernetes
*.yaml.bak
*.yml.bak
k8s/secrets/

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Ansible
*.retry

# Local environment
.local/
