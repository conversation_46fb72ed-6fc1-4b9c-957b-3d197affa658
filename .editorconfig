# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Java files
[*.java]
indent_style = space
indent_size = 4
max_line_length = 120

# XML files
[*.xml]
indent_style = space
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# Properties files
[*.properties]
indent_style = space
indent_size = 4

# JSON files
[*.json]
indent_style = space
indent_size = 2

# JavaScript files
[*.js]
indent_style = space
indent_size = 2

# CSS files
[*.css]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Shell scripts
[*.sh]
indent_style = space
indent_size = 2

# Batch files
[*.bat]
end_of_line = crlf

# Docker files
[Dockerfile*]
indent_style = space
indent_size = 2

# Maven files
[pom.xml]
indent_style = space
indent_size = 4

# SQL files
[*.sql]
indent_style = space
indent_size = 2

# Configuration files
[*.{conf,config,ini}]
indent_style = space
indent_size = 2
