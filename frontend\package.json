{"name": "uav-management-frontend", "version": "1.0.0", "description": "Next.js frontend for UAV Docking Management System", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage --watchAll=false", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@next/font": "^14.0.4", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "lucide-react": "^0.294.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-badge": "^1.0.4", "@radix-ui/react-card": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-input": "^1.0.4", "@radix-ui/react-textarea": "^1.0.4", "zustand": "^4.4.7", "immer": "^10.0.3", "axios": "^1.6.2", "react-query": "^3.39.3", "@tanstack/react-query": "^5.14.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "recharts": "^2.8.0", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "@types/leaflet": "^1.9.8", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.16", "react-hot-toast": "^2.4.1", "cmdk": "^0.2.0"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "@playwright/test": "^1.40.1", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/nextjs": "^7.6.6", "@storybook/react": "^7.6.6", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}