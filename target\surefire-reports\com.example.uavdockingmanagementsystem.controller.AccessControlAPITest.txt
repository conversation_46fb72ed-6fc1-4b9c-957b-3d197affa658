-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.controller.AccessControlAPITest
-------------------------------------------------------------------------------
Tests run: 18, Failures: 18, Errors: 0, Skipped: 0, Time elapsed: 5.375 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.controller.AccessControlAPITest
com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessSuccess -- Time elapsed: 0.137 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessSuccess(AccessControlAPITest.java:40)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessLongRfidId -- Time elapsed: 0.011 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessLongRfidId(AccessControlAPITest.java:194)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessServiceException -- Time elapsed: 0.009 s <<< FAILURE!
java.lang.AssertionError: Status expected:<500> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessServiceException(AccessControlAPITest.java:260)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessMultipleRequests -- Time elapsed: 0.016 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessMultipleRequests(AccessControlAPITest.java:230)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessMissingRfidId -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessMissingRfidId(AccessControlAPITest.java:96)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessLongRegionName -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessLongRegionName(AccessControlAPITest.java:210)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessWithSpecialCharacters -- Time elapsed: 0.009 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessWithSpecialCharacters(AccessControlAPITest.java:148)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessDeleteMethodNotAllowed -- Time elapsed: 0.020 s <<< FAILURE!
java.lang.AssertionError: Status expected:<405> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessDeleteMethodNotAllowed(AccessControlAPITest.java:290)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessNoRegionAccess -- Time elapsed: 0.008 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessNoRegionAccess(AccessControlAPITest.java:85)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessPutMethodNotAllowed -- Time elapsed: 0.005 s <<< FAILURE!
java.lang.AssertionError: Status expected:<405> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessPutMethodNotAllowed(AccessControlAPITest.java:280)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessUAVNotFound -- Time elapsed: 0.010 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessUAVNotFound(AccessControlAPITest.java:55)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessEmptyRfidId -- Time elapsed: 0.009 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessEmptyRfidId(AccessControlAPITest.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessCaseSensitive -- Time elapsed: 0.010 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessCaseSensitive(AccessControlAPITest.java:163)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessWithWhitespace -- Time elapsed: 0.023 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessWithWhitespace(AccessControlAPITest.java:178)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessUnauthorized -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessUnauthorized(AccessControlAPITest.java:70)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessMissingRegionName -- Time elapsed: 0.007 s <<< FAILURE!
java.lang.AssertionError: Status expected:<400> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessMissingRegionName(AccessControlAPITest.java:105)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessEmptyRegionName -- Time elapsed: 0.006 s <<< FAILURE!
java.lang.AssertionError: Status expected:<200> but was:<403>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessEmptyRegionName(AccessControlAPITest.java:133)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessGetMethodNotAllowed -- Time elapsed: 0.022 s <<< FAILURE!
java.lang.AssertionError: Status expected:<405> but was:<401>
	at org.springframework.test.util.AssertionErrors.fail(AssertionErrors.java:61)
	at org.springframework.test.util.AssertionErrors.assertEquals(AssertionErrors.java:128)
	at org.springframework.test.web.servlet.result.StatusResultMatchers.lambda$matcher$9(StatusResultMatchers.java:640)
	at org.springframework.test.web.servlet.MockMvc$1.andExpect(MockMvc.java:214)
	at com.example.uavdockingmanagementsystem.controller.AccessControlAPITest.testValidateAccessGetMethodNotAllowed(AccessControlAPITest.java:270)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

