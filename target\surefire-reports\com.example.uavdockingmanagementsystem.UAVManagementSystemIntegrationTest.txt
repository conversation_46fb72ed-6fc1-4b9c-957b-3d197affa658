-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest
-------------------------------------------------------------------------------
Tests run: 10, Failures: 0, Errors: 10, Skipped: 0, Time elapsed: 0.106 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest
com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testAccessControl -- Time elapsed: 0.013 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testHibernatePodFunctionality -- Time elapsed: 0.009 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testRFIDValidation -- Time elapsed: 0.009 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testStatistics -- Time elapsed: 0.008 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testUAVValidation -- Time elapsed: 0.008 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testHibernatePodAPIEndpoints -- Time elapsed: 0.008 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testCreateAndRetrieveUAV -- Time elapsed: 0.007 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testRegionAssignment -- Time elapsed: 0.009 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testAPIEndpoints -- Time elapsed: 0.009 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.testAccessControlAPI -- Time elapsed: 0.008 s <<< ERROR!
org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more

