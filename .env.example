# UAV Docking Management System - Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Configuration
SPRING_PROFILES_ACTIVE=prod
APP_PORT=8080
LOG_LEVEL=INFO
SECURITY_LOG_LEVEL=WARN

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=uav_management
DB_USER=uav_user
DB_PASSWORD=secure_password
DB_ROOT_PASSWORD=root_password
DDL_AUTO=update
SHOW_SQL=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Security Configuration
JWT_SECRET=your-very-secure-jwt-secret-key-change-in-production-minimum-256-bits

# Nginx Configuration
NGINX_PORT=80
NGINX_SSL_PORT=443

# Docker Configuration
COMPOSE_PROJECT_NAME=uav-management

# Monitoring and Logging
ENABLE_METRICS=true
ENABLE_HEALTH_CHECK=true

# Email Configuration (if using email features)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# External API Configuration
# Add any external API keys or endpoints here

# Development/Testing Configuration
TEST_DB_NAME=uav_test
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# SSL Configuration
SSL_ENABLED=false
SSL_KEYSTORE_PATH=/path/to/keystore.p12
SSL_KEYSTORE_PASSWORD=keystore_password
SSL_KEY_ALIAS=uav-management

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*

# File Upload Configuration
MAX_FILE_SIZE=10MB
MAX_REQUEST_SIZE=10MB

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# WebSocket Configuration
WEBSOCKET_ENABLED=true
WEBSOCKET_ALLOWED_ORIGINS=*

# Actuator Configuration
MANAGEMENT_PORT=8080
MANAGEMENT_CONTEXT_PATH=/actuator

# Timezone Configuration
TZ=UTC
SERVER_TIMEZONE=UTC

# Performance Configuration
HIKARI_MAXIMUM_POOL_SIZE=20
HIKARI_MINIMUM_IDLE=5
HIKARI_CONNECTION_TIMEOUT=30000
HIKARI_IDLE_TIMEOUT=600000
HIKARI_MAX_LIFETIME=1800000
