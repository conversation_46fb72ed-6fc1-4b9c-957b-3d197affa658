-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.service.DockingStationServiceTest
-------------------------------------------------------------------------------
Tests run: 22, Failures: 5, Errors: 3, Skipped: 0, Time elapsed: 0.404 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.service.DockingStationServiceTest
com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUpdateStationNotFound -- Time elapsed: 0.005 s <<< ERROR!
java.lang.RuntimeException: Failed to update station: Station not found with ID: 999
	at com.example.uavdockingmanagementsystem.service.DockingStationService.updateStation(DockingStationService.java:149)
	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUpdateStationNotFound(DockingStationServiceTest.java:134)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVSuccess -- Time elapsed: 0.004 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVSuccess(DockingStationServiceTest.java:253)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testDeleteStation -- Time elapsed: 0.006 s <<< ERROR!
java.lang.RuntimeException: Failed to delete station: Station not found with ID: 1
	at com.example.uavdockingmanagementsystem.service.DockingStationService.deleteStation(DockingStationService.java:175)
	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testDeleteStation(DockingStationServiceTest.java:145)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testDockUAVStationFull -- Time elapsed: 0.005 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testDockUAVStationFull(DockingStationServiceTest.java:219)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testDockUAVStationNotOperational -- Time elapsed: 0.002 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testDockUAVStationNotOperational(DockingStationServiceTest.java:235)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVNotDocked -- Time elapsed: 0.019 s <<< FAILURE!
Wanted but not invoked:
uavRepository.findById(1);
-> at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVNotDocked(DockingStationServiceTest.java:287)
Actually, there were zero interactions with this mock.

	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVNotDocked(DockingStationServiceTest.java:287)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testGetStationStatistics -- Time elapsed: 0.005 s <<< ERROR!
org.mockito.exceptions.misusing.UnnecessaryStubbingException: 

Unnecessary stubbings detected.
Clean & maintainable test code requires zero unnecessary code.
Following stubbings are unnecessary (click to navigate to relevant line of code):
  1. -> at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testGetStationStatistics(DockingStationServiceTest.java:355)
  2. -> at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testGetStationStatistics(DockingStationServiceTest.java:356)
  3. -> at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testGetStationStatistics(DockingStationServiceTest.java:357)
Please remove unnecessary stubbings or use 'lenient' strictness. More info: javadoc for UnnecessaryStubbingException class.
	at org.mockito.junit.jupiter.MockitoExtension.afterEach(MockitoExtension.java:197)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVNotFound -- Time elapsed: 0.007 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <UAV not found> but was: <UAV is not currently docked>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at com.example.uavdockingmanagementsystem.service.DockingStationServiceTest.testUndockUAVNotFound(DockingStationServiceTest.java:271)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

