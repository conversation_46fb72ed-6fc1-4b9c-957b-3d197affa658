import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  UAV,
  UAVFilter,
  PaginationParams,
  SystemStatistics,
  HibernatePodStatus,
  Region,
  CreateUAVRequest,
  UpdateUAVRequest,
} from '@/types/uav';
import { uavApi, hibernatePodApi, regionApi } from '@/api/uav-api';
import { toast } from 'react-hot-toast';

interface UAVState {
  // Data
  uavs: UAV[];
  selectedUAV: UAV | null;
  regions: Region[];
  systemStats: SystemStatistics | null;
  hibernatePodStatus: HibernatePodStatus | null;

  // UI State
  loading: boolean;
  error: string | null;
  filter: UAVFilter;
  pagination: PaginationParams;
  searchQuery: string;

  // Actions
  fetchUAVs: () => Promise<void>;
  fetchUAVById: (id: number) => Promise<void>;
  createUAV: (uav: CreateUAVRequest) => Promise<boolean>;
  updateUAV: (id: number, uav: Partial<UpdateUAVRequest>) => Promise<boolean>;
  deleteUAV: (id: number) => Promise<boolean>;
  updateUAVStatus: (id: number) => Promise<boolean>;
  
  // Region actions
  fetchRegions: () => Promise<void>;
  addRegionToUAV: (uavId: number, regionId: number) => Promise<boolean>;
  removeRegionFromUAV: (uavId: number, regionId: number) => Promise<boolean>;
  
  // Hibernate pod actions
  fetchHibernatePodStatus: () => Promise<void>;
  addToHibernatePod: (uavId: number) => Promise<boolean>;
  removeFromHibernatePod: (uavId: number) => Promise<boolean>;
  
  // Statistics
  fetchSystemStats: () => Promise<void>;
  
  // UI actions
  setFilter: (filter: Partial<UAVFilter>) => void;
  setPagination: (pagination: Partial<PaginationParams>) => void;
  setSearchQuery: (query: string) => void;
  setSelectedUAV: (uav: UAV | null) => void;
  clearError: () => void;
  
  // Bulk actions
  bulkUpdateStatus: (uavIds: number[], status: string) => Promise<boolean>;
  bulkDelete: (uavIds: number[]) => Promise<boolean>;
  
  // Real-time updates
  updateUAVInStore: (uav: UAV) => void;
  removeUAVFromStore: (uavId: number) => void;
  updateHibernatePodInStore: (status: HibernatePodStatus) => void;
}

export const useUAVStore = create<UAVState>()(
  devtools(
    subscribeWithSelector(
      immer((set, get) => ({
        // Initial state
        uavs: [],
        selectedUAV: null,
        regions: [],
        systemStats: null,
        hibernatePodStatus: null,
        loading: false,
        error: null,
        filter: {},
        pagination: { page: 1, limit: 10, sortBy: 'id', sortOrder: 'desc' },
        searchQuery: '',

        // Fetch UAVs
        fetchUAVs: async () => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            const { filter, pagination, searchQuery } = get();
            const finalFilter = searchQuery ? { ...filter, search: searchQuery } : filter;
            const uavs = await uavApi.getUAVs(finalFilter, pagination);
            
            set((state) => {
              state.uavs = uavs;
              state.loading = false;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to fetch UAVs';
              state.loading = false;
            });
          }
        },

        // Fetch UAV by ID
        fetchUAVById: async (id: number) => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            const uav = await uavApi.getUAVById(id);
            set((state) => {
              state.selectedUAV = uav;
              state.loading = false;
            });
          } catch (error) {
            set((state) => {
              state.error = error instanceof Error ? error.message : 'Failed to fetch UAV';
              state.loading = false;
            });
          }
        },

        // Create UAV
        createUAV: async (uav: CreateUAVRequest) => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            const response = await uavApi.createUAV(uav);
            if (response.success && response.data) {
              set((state) => {
                state.uavs.unshift(response.data!);
                state.loading = false;
              });
              toast.success('UAV created successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to create UAV');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to create UAV';
            set((state) => {
              state.error = message;
              state.loading = false;
            });
            toast.error(message);
            return false;
          }
        },

        // Update UAV
        updateUAV: async (id: number, uav: Partial<UpdateUAVRequest>) => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            const response = await uavApi.updateUAV(id, uav);
            if (response.success && response.data) {
              set((state) => {
                const index = state.uavs.findIndex(u => u.id === id);
                if (index !== -1) {
                  state.uavs[index] = response.data!;
                }
                if (state.selectedUAV?.id === id) {
                  state.selectedUAV = response.data!;
                }
                state.loading = false;
              });
              toast.success('UAV updated successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to update UAV');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to update UAV';
            set((state) => {
              state.error = message;
              state.loading = false;
            });
            toast.error(message);
            return false;
          }
        },

        // Delete UAV
        deleteUAV: async (id: number) => {
          set((state) => {
            state.loading = true;
            state.error = null;
          });

          try {
            const response = await uavApi.deleteUAV(id);
            if (response.success) {
              set((state) => {
                state.uavs = state.uavs.filter(u => u.id !== id);
                if (state.selectedUAV?.id === id) {
                  state.selectedUAV = null;
                }
                state.loading = false;
              });
              toast.success('UAV deleted successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to delete UAV');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to delete UAV';
            set((state) => {
              state.error = message;
              state.loading = false;
            });
            toast.error(message);
            return false;
          }
        },

        // Update UAV status
        updateUAVStatus: async (id: number) => {
          try {
            const response = await uavApi.updateUAVStatus(id);
            if (response.success) {
              set((state) => {
                const index = state.uavs.findIndex(u => u.id === id);
                if (index !== -1) {
                  // Toggle status
                  const currentStatus = state.uavs[index].status;
                  state.uavs[index].status = currentStatus === 'AUTHORIZED' ? 'UNAUTHORIZED' : 'AUTHORIZED';
                }
              });
              toast.success('UAV status updated successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to update UAV status');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to update UAV status';
            toast.error(message);
            return false;
          }
        },

        // Fetch regions
        fetchRegions: async () => {
          try {
            const regions = await regionApi.getRegions();
            set((state) => {
              state.regions = regions;
            });
          } catch (error) {
            console.error('Failed to fetch regions:', error);
          }
        },

        // Add region to UAV
        addRegionToUAV: async (uavId: number, regionId: number) => {
          try {
            const response = await uavApi.addRegionToUAV(uavId, regionId);
            if (response.success && response.data) {
              set((state) => {
                const index = state.uavs.findIndex(u => u.id === uavId);
                if (index !== -1) {
                  state.uavs[index] = response.data!;
                }
              });
              toast.success('Region added to UAV successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to add region to UAV');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to add region to UAV';
            toast.error(message);
            return false;
          }
        },

        // Remove region from UAV
        removeRegionFromUAV: async (uavId: number, regionId: number) => {
          try {
            const response = await uavApi.removeRegionFromUAV(uavId, regionId);
            if (response.success) {
              set((state) => {
                const index = state.uavs.findIndex(u => u.id === uavId);
                if (index !== -1) {
                  state.uavs[index].regions = state.uavs[index].regions.filter(r => r.id !== regionId);
                }
              });
              toast.success('Region removed from UAV successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to remove region from UAV');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to remove region from UAV';
            toast.error(message);
            return false;
          }
        },

        // Fetch hibernate pod status
        fetchHibernatePodStatus: async () => {
          try {
            const status = await hibernatePodApi.getStatus();
            set((state) => {
              state.hibernatePodStatus = status;
            });
          } catch (error) {
            console.error('Failed to fetch hibernate pod status:', error);
          }
        },

        // Add to hibernate pod
        addToHibernatePod: async (uavId: number) => {
          try {
            const response = await hibernatePodApi.addUAV(uavId);
            if (response.success) {
              set((state) => {
                const index = state.uavs.findIndex(u => u.id === uavId);
                if (index !== -1) {
                  state.uavs[index].inHibernatePod = true;
                  state.uavs[index].operationalStatus = 'HIBERNATING';
                }
              });
              // Refresh hibernate pod status
              get().fetchHibernatePodStatus();
              toast.success('UAV added to hibernate pod successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to add UAV to hibernate pod');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to add UAV to hibernate pod';
            toast.error(message);
            return false;
          }
        },

        // Remove from hibernate pod
        removeFromHibernatePod: async (uavId: number) => {
          try {
            const response = await hibernatePodApi.removeUAV(uavId);
            if (response.success) {
              set((state) => {
                const index = state.uavs.findIndex(u => u.id === uavId);
                if (index !== -1) {
                  state.uavs[index].inHibernatePod = false;
                  state.uavs[index].operationalStatus = 'READY';
                }
              });
              // Refresh hibernate pod status
              get().fetchHibernatePodStatus();
              toast.success('UAV removed from hibernate pod successfully');
              return true;
            } else {
              throw new Error(response.message || 'Failed to remove UAV from hibernate pod');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to remove UAV from hibernate pod';
            toast.error(message);
            return false;
          }
        },

        // Fetch system statistics
        fetchSystemStats: async () => {
          try {
            const stats = await uavApi.getSystemStatistics();
            set((state) => {
              state.systemStats = stats;
            });
          } catch (error) {
            console.error('Failed to fetch system statistics:', error);
          }
        },

        // UI actions
        setFilter: (filter: Partial<UAVFilter>) => {
          set((state) => {
            state.filter = { ...state.filter, ...filter };
          });
        },

        setPagination: (pagination: Partial<PaginationParams>) => {
          set((state) => {
            state.pagination = { ...state.pagination, ...pagination };
          });
        },

        setSearchQuery: (query: string) => {
          set((state) => {
            state.searchQuery = query;
          });
        },

        setSelectedUAV: (uav: UAV | null) => {
          set((state) => {
            state.selectedUAV = uav;
          });
        },

        clearError: () => {
          set((state) => {
            state.error = null;
          });
        },

        // Bulk actions
        bulkUpdateStatus: async (uavIds: number[], status: string) => {
          try {
            const response = await uavApi.bulkUpdateStatus(uavIds, status);
            if (response.success) {
              set((state) => {
                uavIds.forEach(id => {
                  const index = state.uavs.findIndex(u => u.id === id);
                  if (index !== -1) {
                    state.uavs[index].status = status as any;
                  }
                });
              });
              toast.success(`${uavIds.length} UAVs updated successfully`);
              return true;
            } else {
              throw new Error(response.message || 'Failed to update UAVs');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to update UAVs';
            toast.error(message);
            return false;
          }
        },

        bulkDelete: async (uavIds: number[]) => {
          try {
            const response = await uavApi.bulkDelete(uavIds);
            if (response.success) {
              set((state) => {
                state.uavs = state.uavs.filter(u => !uavIds.includes(u.id));
              });
              toast.success(`${uavIds.length} UAVs deleted successfully`);
              return true;
            } else {
              throw new Error(response.message || 'Failed to delete UAVs');
            }
          } catch (error) {
            const message = error instanceof Error ? error.message : 'Failed to delete UAVs';
            toast.error(message);
            return false;
          }
        },

        // Real-time updates
        updateUAVInStore: (uav: UAV) => {
          set((state) => {
            const index = state.uavs.findIndex(u => u.id === uav.id);
            if (index !== -1) {
              state.uavs[index] = uav;
            } else {
              state.uavs.unshift(uav);
            }
            if (state.selectedUAV?.id === uav.id) {
              state.selectedUAV = uav;
            }
          });
        },

        removeUAVFromStore: (uavId: number) => {
          set((state) => {
            state.uavs = state.uavs.filter(u => u.id !== uavId);
            if (state.selectedUAV?.id === uavId) {
              state.selectedUAV = null;
            }
          });
        },

        updateHibernatePodInStore: (status: HibernatePodStatus) => {
          set((state) => {
            state.hibernatePodStatus = status;
          });
        },
      }))
    ),
    {
      name: 'uav-store',
    }
  )
);
