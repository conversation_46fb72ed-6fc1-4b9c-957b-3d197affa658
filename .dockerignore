# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
LICENSE
*.md
docs/

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
target/
!target/*.jar
*.class
*.log
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Maven
.mvn/wrapper/maven-wrapper.jar
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.properties

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Logs
logs/
*.log
log/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Security files
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore
secrets/

# Test files
src/test/
**/test/
**/*Test.java
**/*Tests.java

# Development files
dev/
development/
local/

# Backup files
*.bak
*.backup
*.orig

# Cache directories
.cache/

# Application specific
uploads/
downloads/
temp/
cache/

# Docker files (avoid recursive Docker builds)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD files
.github/
.gitlab-ci.yml
Jenkinsfile

# Scripts (they're not needed in the container)
*.sh
*.bat
*.cmd

# Configuration files that shouldn't be in the image
checkstyle.xml
.editorconfig
spotbugs-exclude.xml

# Maven wrapper (we copy it explicitly)
mvnw
mvnw.cmd

# Database files
*.db
*.sqlite
*.sqlite3

# Monitoring and metrics
metrics/
monitoring/
