<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" version="3.0.2" name="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.106" tests="10" errors="10" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\Project\DaChuangBackend\target\test-classes;D:\Project\DaChuangBackend\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.4.4\spring-boot-starter-web-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.4.4\spring-boot-starter-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.4.4\spring-boot-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.4\spring-boot-autoconfigure-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.4.4\spring-boot-starter-logging-3.4.4.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.4.4\spring-boot-starter-json-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.3\jackson-datatype-jdk8-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.3\jackson-module-parameter-names-2.18.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.4.4\spring-boot-starter-tomcat-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.39\tomcat-embed-core-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.39\tomcat-embed-websocket-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.5\spring-web-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.5\spring-beans-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.5\spring-webmvc-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.5\spring-context-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.5\spring-expression-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.4.4\spring-boot-starter-test-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.4.4\spring-boot-test-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.4.4\spring-boot-test-autoconfigure-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.26.3\assertj-core-3.26.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.15.11\byte-buddy-1.15.11.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.11.4\junit-jupiter-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.11.4\junit-jupiter-api-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.11.4\junit-platform-commons-1.11.4.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.11.4\junit-jupiter-params-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.11.4\junit-jupiter-engine-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.11.4\junit-platform-engine-1.11.4.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.14.2\mockito-core-5.14.2.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.15.11\byte-buddy-agent-1.15.11.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.14.2\mockito-junit-jupiter-5.14.2.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.5\spring-core-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.5\spring-jcl-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.2.5\spring-test-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.10.0\xmlunit-core-2.10.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.4.4\spring-boot-starter-data-jpa-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.4.4\spring-boot-starter-jdbc-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.5\spring-jdbc-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.6.11.Final\hibernate-core-6.6.11.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\7.0.3.Final\hibernate-commons-annotations-7.0.3.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.2.0\jandex-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.4.4\spring-data-jpa-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.4.4\spring-data-commons-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.2.5\spring-orm-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.5\spring-tx-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.2.5\spring-aspects-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.23\aspectjweaver-1.9.23.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-thymeleaf\3.4.4\spring-boot-starter-thymeleaf-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf-spring6\3.1.3.RELEASE\thymeleaf-spring6-3.1.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf\3.1.3.RELEASE\thymeleaf-3.1.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\attoparser\attoparser\2.0.7.RELEASE\attoparser-2.0.7.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\2.3.232\h2-2.3.232.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.4.4\spring-boot-starter-security-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.5\spring-aop-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.4.4\spring-security-config-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.4.4\spring-security-core-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.4.4\spring-security-crypto-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.4.4\spring-security-web-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\3.4.4\spring-boot-starter-websocket-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\6.2.5\spring-messaging-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\6.2.5\spring-websocket-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\3.4.4\spring-boot-starter-mail-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.2.5\spring-context-support-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\jakarta.mail\2.0.3\jakarta.mail-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.4.4\spring-boot-starter-validation-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.39\tomcat-embed-el-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.4\spring-boot-starter-actuator-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.4\spring-boot-actuator-autoconfigure-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.4.4\spring-boot-actuator-3.4.4.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.14.5\micrometer-observation-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.14.5\micrometer-commons-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.14.5\micrometer-jakarta9-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.14.5\micrometer-core-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.3\jackson-annotations-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.18.3\jackson-core-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.18.3\jackson-databind-2.18.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-graphql\3.4.4\spring-boot-starter-graphql-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\graphql\spring-graphql\1.3.4\spring-graphql-1.3.4.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.7.4\reactor-core-3.7.4.jar;C:\Users\<USER>\.m2\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java-extended-scalars\20.2\graphql-java-extended-scalars-20.2.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java\22.3\graphql-java-22.3.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\java-dataloader\3.3.0\java-dataloader-3.3.0.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.4.4\spring-boot-starter-data-redis-3.4.4.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.4.2.RELEASE\lettuce-core-6.4.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.119.Final\netty-common-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.119.Final\netty-handler-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.119.Final\netty-resolver-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.119.Final\netty-buffer-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.119.Final\netty-transport-native-unix-common-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.119.Final\netty-codec-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.119.Final\netty-transport-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.4.4\spring-data-redis-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.4.4\spring-data-keyvalue-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.2.5\spring-oxm-6.2.5.jar;"/>
    <property name="java.vm.vendor" value="Microsoft"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://www.microsoft.com"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="APPLICATION_NAME" value="UAV-Docking-Management-System-Test"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Program Files\Microsoft\jdk-********-hotspot\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire17960443307988402959\surefirebooter-20250728205824433_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire17960443307988402959 2025-07-28T20-58-24_230-jvmRun1 surefire-20250728205824433_1tmp surefire_0-20250728205824433_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\Project\DaChuangBackend\target\test-classes;D:\Project\DaChuangBackend\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\3.4.4\spring-boot-starter-web-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\3.4.4\spring-boot-starter-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\3.4.4\spring-boot-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\3.4.4\spring-boot-autoconfigure-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\3.4.4\spring-boot-starter-logging-3.4.4.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.5.18\logback-classic-1.5.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.5.18\logback-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\2.0.17\jul-to-slf4j-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\3.4.4\spring-boot-starter-json-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.3\jackson-datatype-jdk8-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.3\jackson-module-parameter-names-2.18.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\3.4.4\spring-boot-starter-tomcat-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.39\tomcat-embed-core-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.39\tomcat-embed-websocket-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\6.2.5\spring-web-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\6.2.5\spring-beans-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\6.2.5\spring-webmvc-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\6.2.5\spring-context-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\6.2.5\spring-expression-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\3.4.4\spring-boot-starter-test-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\3.4.4\spring-boot-test-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.4.4\spring-boot-test-autoconfigure-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.17\slf4j-api-2.0.17.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.26.3\assertj-core-3.26.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.15.11\byte-buddy-1.15.11.jar;C:\Users\<USER>\.m2\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.11.4\junit-jupiter-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.11.4\junit-jupiter-api-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.11.4\junit-platform-commons-1.11.4.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.11.4\junit-jupiter-params-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.11.4\junit-jupiter-engine-5.11.4.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.11.4\junit-platform-engine-1.11.4.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.14.2\mockito-core-5.14.2.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.15.11\byte-buddy-agent-1.15.11.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\5.14.2\mockito-junit-jupiter-5.14.2.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\6.2.5\spring-core-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\6.2.5\spring-jcl-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\6.2.5\spring-test-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.10.0\xmlunit-core-2.10.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.4.4\spring-boot-starter-data-jpa-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\3.4.4\spring-boot-starter-jdbc-3.4.4.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\6.2.5\spring-jdbc-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\hibernate\orm\hibernate-core\6.6.11.Final\hibernate-core-6.6.11.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\Users\<USER>\.m2\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar;C:\Users\<USER>\.m2\repository\org\hibernate\common\hibernate-commons-annotations\7.0.3.Final\hibernate-commons-annotations-7.0.3.Final.jar;C:\Users\<USER>\.m2\repository\io\smallrye\jandex\3.2.0\jandex-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\4.0.5\jaxb-runtime-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\Users\<USER>\.m2\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\Users\<USER>\.m2\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-jpa\3.4.4\spring-data-jpa-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\3.4.4\spring-data-commons-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\6.2.5\spring-orm-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\6.2.5\spring-tx-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\6.2.5\spring-aspects-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.23\aspectjweaver-1.9.23.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-thymeleaf\3.4.4\spring-boot-starter-thymeleaf-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf-spring6\3.1.3.RELEASE\thymeleaf-spring6-3.1.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\thymeleaf\thymeleaf\3.1.3.RELEASE\thymeleaf-3.1.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\attoparser\attoparser\2.0.7.RELEASE\attoparser-2.0.7.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\8.0.33\mysql-connector-j-8.0.33.jar;C:\Users\<USER>\.m2\repository\com\h2database\h2\2.3.232\h2-2.3.232.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\3.4.4\spring-boot-starter-security-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\6.2.5\spring-aop-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\6.4.4\spring-security-config-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\6.4.4\spring-security-core-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\6.4.4\spring-security-crypto-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\6.4.4\spring-security-web-6.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-websocket\3.4.4\spring-boot-starter-websocket-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-messaging\6.2.5\spring-messaging-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-websocket\6.2.5\spring-websocket-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\3.4.4\spring-boot-starter-mail-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\6.2.5\spring-context-support-6.2.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\jakarta.mail\2.0.3\jakarta.mail-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-validation\3.4.4\spring-boot-starter-validation-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.39\tomcat-embed-el-10.1.39.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar;C:\Users\<USER>\.m2\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\3.4.4\spring-boot-starter-actuator-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.4.4\spring-boot-actuator-autoconfigure-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\3.4.4\spring-boot-actuator-3.4.4.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-observation\1.14.5\micrometer-observation-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-commons\1.14.5\micrometer-commons-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-jakarta9\1.14.5\micrometer-jakarta9-1.14.5.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.14.5\micrometer-core-1.14.5.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.2.2\HdrHistogram-2.2.2.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.3\jackson-datatype-jsr310-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.18.3\jackson-annotations-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.18.3\jackson-core-2.18.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.18.3\jackson-databind-2.18.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-graphql\3.4.4\spring-boot-starter-graphql-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\graphql\spring-graphql\1.3.4\spring-graphql-1.3.4.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.7.4\reactor-core-3.7.4.jar;C:\Users\<USER>\.m2\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java-extended-scalars\20.2\graphql-java-extended-scalars-20.2.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\graphql-java\22.3\graphql-java-22.3.jar;C:\Users\<USER>\.m2\repository\com\graphql-java\java-dataloader\3.3.0\java-dataloader-3.3.0.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\3.4.4\spring-boot-starter-data-redis-3.4.4.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.4.2.RELEASE\lettuce-core-6.4.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.119.Final\netty-common-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.119.Final\netty-handler-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.119.Final\netty-resolver-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.119.Final\netty-buffer-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.119.Final\netty-transport-native-unix-common-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.119.Final\netty-codec-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.119.Final\netty-transport-4.1.119.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\3.4.4\spring-data-redis-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\3.4.4\spring-data-keyvalue-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\6.2.5\spring-oxm-6.2.5.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Microsoft\jdk-********-hotspot"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\Project\DaChuangBackend"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire17960443307988402959\surefirebooter-20250728205824433_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+6-LTS"/>
    <property name="user.name" value="qwdma"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Microsoft-11369940"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/microsoft/openjdk/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="com.zaxxer.hikari.pool_number" value="2"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="D:\Project\DaChuangBackend"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="80228"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Program Files\Microsoft\jdk-********-hotspot\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\Program Files\Microsoft\jdk-********-hotspot\bin;D:\Program Files\Microsoft\jdk-********-hotspot\bin;D:\java17\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Bandizip\;D:\Software\vcpkg;D:\Project\CMake\bin;D:\msys64\mingw64\bin;C:\Program Files\dotnet\;C:\Program Files\GitHub CLI\;C:\Program Files\usbipd-win\;D:\Program Files\Git\cmd;D:\Program Files\nodejs\;D:\Program Files\PuTTY\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\Program Files\CMake\bin;C:\Program Files (x86)\Incredibuild;C:\Users\<USER>\scoop\shims;D:\Python\Scripts\;D:\Python\;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Muse Hub\lib;D:\ninja-win;C:\Program Files\nodejs\;C:\Users\<USER>\.lmstudio\bin;D:\java17\bin;D:\Android Studio\jbr\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\36.0.0;C:\Users\<USER>\.bun\bin;D:\Software\act_Windows_x86_64;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Program Files\Microsoft\jdk-********-hotspot\bin;D:\Users\qwdma\AppData\Local\Programs\Kiro\bin;C:\Users\<USER>\.dotnet\tools;D:\Users\qwdma\AppData\Local\Programs\Windsurf\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Microsoft"/>
    <property name="java.vm.version" value="21.0.7+6-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[UAV-Docking-Management-System-Test] "/>
  </properties>
  <testcase name="testAccessControl" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.013">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:49.992 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:49.992 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testHibernatePodFunctionality" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.009">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.005 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.005 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testRFIDValidation" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.009">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.015 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.015 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testStatistics" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.008">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.024 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.024 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testUAVValidation" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.008">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.033 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.033 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testHibernatePodAPIEndpoints" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.008">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.042 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.042 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testCreateAndRetrieveUAV" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.007">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.051 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.051 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testRegionAssignment" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.009">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.061 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.061 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testAPIEndpoints" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.009">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.069 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.069 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
  <testcase name="testAccessControlAPI" classname="com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest" time="0.008">
    <error message="could not prepare statement [Table &quot;UAV&quot; not found (this database is empty); SQL statement:&#10;select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]" type="org.springframework.dao.InvalidDataAccessResourceUsageException"><![CDATA[org.springframework.dao.InvalidDataAccessResourceUsageException: 
could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]; SQL [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:560)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:343)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:165)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy228.deleteAll(Unknown Source)
	at com.example.uavdockingmanagementsystem.UAVManagementSystemIntegrationTest.setUp(UAVManagementSystemIntegrationTest.java:66)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: org.hibernate.exception.SQLGrammarException: could not prepare statement [Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]] [select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0]
	at org.hibernate.exception.internal.SQLExceptionTypeDelegate.convert(SQLExceptionTypeDelegate.java:66)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:191)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl.prepareQueryStatement(StatementPreparerImpl.java:153)
	at org.hibernate.sql.exec.internal.StandardStatementCreator.createStatement(StandardStatementCreator.java:49)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:235)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:171)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.<init>(JdbcValuesResultSetImpl.java:74)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.resolveJdbcValuesSource(JdbcSelectExecutorStandardImpl.java:355)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:137)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:102)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.executeQuery(JdbcSelectExecutor.java:91)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:165)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$1(ConcreteSqmSelectQueryPlan.java:152)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:442)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:362)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:380)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:143)
	at org.hibernate.query.Query.getResultList(Query.java:120)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.findAll(SimpleJpaRepository.java:395)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.deleteAll(SimpleJpaRepository.java:294)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:515)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:284)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:731)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:174)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:149)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:69)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 9 more
Caused by: org.h2.jdbc.JdbcSQLSyntaxErrorException: Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:514)
	at org.h2.message.DbException.getJdbcSQLException(DbException.java:489)
	at org.h2.message.DbException.get(DbException.java:223)
	at org.h2.message.DbException.get(DbException.java:199)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7932)
	at org.h2.command.Parser.getTableOrViewNotFoundDbException(Parser.java:7916)
	at org.h2.command.Parser.readTableOrView(Parser.java:7895)
	at org.h2.command.Parser.readTablePrimary(Parser.java:1769)
	at org.h2.command.Parser.readTableReference(Parser.java:2249)
	at org.h2.command.Parser.parseSelectFromPart(Parser.java:2702)
	at org.h2.command.Parser.parseSelect(Parser.java:2810)
	at org.h2.command.Parser.parseQueryPrimary(Parser.java:2692)
	at org.h2.command.Parser.parseQueryTerm(Parser.java:2547)
	at org.h2.command.Parser.parseQueryExpressionBody(Parser.java:2526)
	at org.h2.command.Parser.parseQueryExpressionBodyAndEndOfQuery(Parser.java:2519)
	at org.h2.command.Parser.parseQueryExpression(Parser.java:2512)
	at org.h2.command.Parser.parseQuery(Parser.java:2479)
	at org.h2.command.Parser.parsePrepared(Parser.java:610)
	at org.h2.command.Parser.parse(Parser.java:581)
	at org.h2.command.Parser.parse(Parser.java:556)
	at org.h2.command.Parser.prepareCommand(Parser.java:484)
	at org.h2.engine.SessionLocal.prepareLocal(SessionLocal.java:645)
	at org.h2.engine.SessionLocal.prepareCommand(SessionLocal.java:561)
	at org.h2.jdbc.JdbcConnection.prepareCommand(JdbcConnection.java:1164)
	at org.h2.jdbc.JdbcPreparedStatement.<init>(JdbcPreparedStatement.java:93)
	at org.h2.jdbc.JdbcConnection.prepareStatement(JdbcConnection.java:687)
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:342)
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$4.doPrepare(StatementPreparerImpl.java:151)
	at org.hibernate.engine.jdbc.internal.StatementPreparerImpl$StatementPreparationTemplate.prepareStatement(StatementPreparerImpl.java:180)
	... 46 more
]]></error>
    <system-out><![CDATA[2025-07-28 20:58:50.079 [main] &amp#27;[31mWARN &amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - SQL Error: 42104, SQLState: 42S04
2025-07-28 20:58:50.079 [main] &amp#27;[1;31mERROR&amp#27;[0;39m &amp#27;[36mo.h.e.jdbc.spi.SqlExceptionHelper&amp#27;[0;39m - Table "UAV" not found (this database is empty); SQL statement:
select u1_0.id,u1_0.created_at,u1_0.current_altitude_meters,u1_0.current_latitude,u1_0.current_location_latitude,u1_0.current_location_longitude,u1_0.current_longitude,u1_0.in_hibernate_pod,u1_0.last_known_location_update,u1_0.last_location_update,u1_0.last_maintenance_date,u1_0.manufacturer,u1_0.max_altitude_meters,u1_0.max_flight_time_minutes,u1_0.max_speed_kmh,u1_0.model,u1_0.next_maintenance_due,u1_0.operational_status,u1_0.owner_name,u1_0.rfid_tag,u1_0.serial_number,u1_0.status,u1_0.total_flight_cycles,u1_0.total_flight_hours,u1_0.updated_at,u1_0.weight_kg from uav u1_0 [42104-232]
]]></system-out>
  </testcase>
</testsuite>