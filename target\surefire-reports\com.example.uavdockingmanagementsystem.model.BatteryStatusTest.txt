-------------------------------------------------------------------------------
Test set: com.example.uavdockingmanagementsystem.model.BatteryStatusTest
-------------------------------------------------------------------------------
Tests run: 16, Failures: 1, Errors: 2, Skipped: 0, Time elapsed: 0.044 s <<< FAILURE! -- in com.example.uavdockingmanagementsystem.model.BatteryStatusTest
com.example.uavdockingmanagementsystem.model.BatteryStatusTest.testDefaultValues -- Time elapsed: 0.003 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <null> but was: <0>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertNull.failNotNull(AssertNull.java:50)
	at org.junit.jupiter.api.AssertNull.assertNull(AssertNull.java:35)
	at org.junit.jupiter.api.AssertNull.assertNull(AssertNull.java:30)
	at org.junit.jupiter.api.Assertions.assertNull(Assertions.java:279)
	at com.example.uavdockingmanagementsystem.model.BatteryStatusTest.testDefaultValues(BatteryStatusTest.java:65)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.model.BatteryStatusTest.testIsLowBattery -- Time elapsed: 0.002 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because "this.currentChargePercentage" is null
	at com.example.uavdockingmanagementsystem.model.BatteryStatus.isLowBattery(BatteryStatus.java:396)
	at com.example.uavdockingmanagementsystem.model.BatteryStatusTest.testIsLowBattery(BatteryStatusTest.java:112)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.example.uavdockingmanagementsystem.model.BatteryStatusTest.testIsCriticalBattery -- Time elapsed: 0.002 s <<< ERROR!
java.lang.NullPointerException: Cannot invoke "java.lang.Integer.intValue()" because "this.currentChargePercentage" is null
	at com.example.uavdockingmanagementsystem.model.BatteryStatus.isCriticalBattery(BatteryStatus.java:400)
	at com.example.uavdockingmanagementsystem.model.BatteryStatusTest.testIsCriticalBattery(BatteryStatusTest.java:132)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

